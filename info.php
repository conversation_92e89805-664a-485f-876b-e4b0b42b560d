<?php
require_once 'config/database.php';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Attendance System - Access Information</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
        }
        
        .info-container {
            background: white;
            padding: 3rem;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            width: 100%;
        }
        
        .info-container h1 {
            color: #333;
            margin-bottom: 2rem;
            text-align: center;
            font-size: 2rem;
        }
        
        .url-section {
            margin-bottom: 2rem;
        }
        
        .url-section h3 {
            color: #667eea;
            margin-bottom: 1rem;
            font-size: 1.2rem;
        }
        
        .url-box {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            border-left: 4px solid #667eea;
            margin-bottom: 1rem;
        }
        
        .url-box label {
            font-weight: 600;
            color: #333;
            display: block;
            margin-bottom: 0.5rem;
        }
        
        .url-box input {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
            background: white;
        }
        
        .copy-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 0.5rem;
            font-size: 0.9rem;
        }
        
        .copy-btn:hover {
            background: #5a6fd8;
        }
        
        .note {
            background: #fff3cd;
            color: #856404;
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid #ffeaa7;
            margin-top: 2rem;
        }
        
        .note h4 {
            margin-bottom: 0.5rem;
        }
        
        .back-btn {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0.75rem 1.5rem;
            text-decoration: none;
            border-radius: 8px;
            margin-top: 1rem;
            text-align: center;
        }
        
        .back-btn:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="info-container">
        <h1>🕐 Attendance System Access</h1>
        
        <div class="url-section">
            <h3>Main Website (Local Access)</h3>
            <div class="url-box">
                <label>Local URL:</label>
                <input type="text" value="http://localhost/attendance website/" readonly id="localUrl">
                <button class="copy-btn" onclick="copyToClipboard('localUrl')">Copy</button>
            </div>
        </div>
        
        <div class="url-section">
            <h3>User Login (Network Access)</h3>
            <div class="url-box">
                <label>Network URL for User Login:</label>
                <input type="text" value="<?php echo BASE_URL; ?>user_login.php" readonly id="networkUserUrl">
                <button class="copy-btn" onclick="copyToClipboard('networkUserUrl')">Copy</button>
            </div>
        </div>
        
        <div class="url-section">
            <h3>User Registration (Network Access)</h3>
            <div class="url-box">
                <label>Network URL for User Registration:</label>
                <input type="text" value="<?php echo BASE_URL; ?>user_registration.php" readonly id="networkRegUrl">
                <button class="copy-btn" onclick="copyToClipboard('networkRegUrl')">Copy</button>
            </div>
        </div>
        
        <div class="url-section">
            <h3>Admin Access (Network Access)</h3>
            <div class="url-box">
                <label>Network URL for Admin:</label>
                <input type="text" value="<?php echo BASE_URL; ?>admin_login.php" readonly id="networkAdminUrl">
                <button class="copy-btn" onclick="copyToClipboard('networkAdminUrl')">Copy</button>
            </div>
        </div>
        
        <div class="note">
            <h4>📝 Important Notes:</h4>
            <ul>
                <li><strong>Server IP:</strong> <?php echo SERVER_IP; ?></li>
                <li><strong>Protocol:</strong> HTTPS (Users will see security warning - they can proceed safely)</li>
                <li><strong>Admin Credentials:</strong> Username: admin, Password: password</li>
                <li><strong>Device Verification:</strong> Users must register and login from the same device</li>
                <li><strong>Clean Rollout:</strong> All device registrations have been reset - users need to re-register</li>
                <li><strong>Database:</strong> Import staff_clean.sql for fresh start</li>
            </ul>
        </div>
        
        <div style="text-align: center;">
            <a href="index.php" class="back-btn">← Back to Main Page</a>
        </div>
    </div>
    
    <script>
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            element.select();
            element.setSelectionRange(0, 99999);
            
            try {
                document.execCommand('copy');
                
                // Show feedback
                const button = element.nextElementSibling;
                const originalText = button.textContent;
                button.textContent = 'Copied!';
                button.style.background = '#28a745';
                
                setTimeout(() => {
                    button.textContent = originalText;
                    button.style.background = '#667eea';
                }, 2000);
            } catch (err) {
                console.error('Failed to copy: ', err);
            }
        }
    </script>
</body>
</html>
