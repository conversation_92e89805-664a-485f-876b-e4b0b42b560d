<?php
// Database configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'attendance_db');
define('DB_USER', 'root');
define('DB_PASS', '');

// Create database connection
function getDBConnection() {
    try {
        $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        return $pdo;
    } catch(PDOException $e) {
        die("Connection failed: " . $e->getMessage());
    }
}

// Start session if not already started
function startSession() {
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
}

// Check if admin is logged in
function isAdminLoggedIn() {
    startSession();
    return isset($_SESSION['admin_id']);
}

// Check if user is logged in
function isUserLoggedIn() {
    startSession();
    return isset($_SESSION['user_id']);
}

// Redirect if not admin
function requireAdmin() {
    if (!isAdminLoggedIn()) {
        header('Location: admin_login.php');
        exit();
    }
}

// Redirect if not user
function requireUser() {
    if (!isUserLoggedIn()) {
        header('Location: user_login.php');
        exit();
    }
}

// Get device information from user agent
function getDeviceInfo() {
    $userAgent = $_SERVER['HTTP_USER_AGENT'];
    
    // Detect device type and model
    if (preg_match('/iPhone/', $userAgent)) {
        return 'iPhone';
    } elseif (preg_match('/Android (\d+)/', $userAgent, $matches)) {
        return 'Android ' . $matches[1];
    } elseif (preg_match('/Android/', $userAgent)) {
        return 'Android';
    } else {
        return 'Unknown Device';
    }
}

// Generate device token based on user agent and other factors
function generateDeviceToken() {
    $userAgent = $_SERVER['HTTP_USER_AGENT'];
    $ip = $_SERVER['REMOTE_ADDR'];
    $timestamp = time();
    
    return hash('sha256', $userAgent . $ip . $timestamp);
}

// Sanitize input
function sanitizeInput($input) {
    return htmlspecialchars(strip_tags(trim($input)));
}

// Generate registration ID
function generateRegistrationId($staffId) {
    return 'REG_' . $staffId . '_' . date('Ymd_His');
}
?>
