<?php
// Database configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'attendance_db');
define('DB_USER', 'root');
define('DB_PASS', '');

// Server configuration
define('SERVER_IP', '*************');
define('BASE_URL', 'https://' . SERVER_IP . '/attendance website/');
define('SITE_ROOT', '/attendance website/');

// Create database connection
function getDBConnection() {
    try {
        $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        return $pdo;
    } catch(PDOException $e) {
        die("Connection failed: " . $e->getMessage());
    }
}

// Start session if not already started
function startSession() {
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
}

// Check if admin is logged in
function isAdminLoggedIn() {
    startSession();
    return isset($_SESSION['admin_id']);
}

// Check if user is logged in
function isUserLoggedIn() {
    startSession();
    return isset($_SESSION['user_id']);
}

// Redirect if not admin
function requireAdmin() {
    if (!isAdminLoggedIn()) {
        header('Location: admin_login.php');
        exit();
    }
}

// Redirect if not user
function requireUser() {
    if (!isUserLoggedIn()) {
        header('Location: user_login.php');
        exit();
    }
}

// Get device information from user agent
function getDeviceInfo() {
    $userAgent = $_SERVER['HTTP_USER_AGENT'];
    $deviceInfo = [];

    // Operating System Detection
    if (preg_match('/iPhone OS (\d+[_\d]*)/', $userAgent, $matches)) {
        $deviceInfo['os'] = 'iOS ' . str_replace('_', '.', $matches[1]);
        $deviceInfo['device'] = 'iPhone';
    } elseif (preg_match('/iPad.*OS (\d+[_\d]*)/', $userAgent, $matches)) {
        $deviceInfo['os'] = 'iPadOS ' . str_replace('_', '.', $matches[1]);
        $deviceInfo['device'] = 'iPad';
    } elseif (preg_match('/Android (\d+[\.\d]*)/', $userAgent, $matches)) {
        $deviceInfo['os'] = 'Android ' . $matches[1];
        if (preg_match('/Mobile/', $userAgent)) {
            $deviceInfo['device'] = 'Android Phone';
        } else {
            $deviceInfo['device'] = 'Android Tablet';
        }
    } elseif (preg_match('/Windows NT (\d+\.\d+)/', $userAgent, $matches)) {
        $deviceInfo['os'] = 'Windows ' . $matches[1];
        $deviceInfo['device'] = 'Windows PC';
    } elseif (preg_match('/Mac OS X (\d+[_\d]*)/', $userAgent, $matches)) {
        $deviceInfo['os'] = 'macOS ' . str_replace('_', '.', $matches[1]);
        $deviceInfo['device'] = 'Mac';
    } elseif (preg_match('/Linux/', $userAgent)) {
        $deviceInfo['os'] = 'Linux';
        $deviceInfo['device'] = 'Linux PC';
    } else {
        $deviceInfo['os'] = 'Unknown OS';
        $deviceInfo['device'] = 'Unknown Device';
    }

    // Browser Detection
    if (preg_match('/Chrome\/(\d+[\.\d]*)/', $userAgent, $matches)) {
        $deviceInfo['browser'] = 'Chrome ' . $matches[1];
    } elseif (preg_match('/Firefox\/(\d+[\.\d]*)/', $userAgent, $matches)) {
        $deviceInfo['browser'] = 'Firefox ' . $matches[1];
    } elseif (preg_match('/Safari\/(\d+[\.\d]*)/', $userAgent, $matches) && !preg_match('/Chrome/', $userAgent)) {
        $deviceInfo['browser'] = 'Safari ' . $matches[1];
    } elseif (preg_match('/Edge\/(\d+[\.\d]*)/', $userAgent, $matches)) {
        $deviceInfo['browser'] = 'Edge ' . $matches[1];
    } else {
        $deviceInfo['browser'] = 'Unknown Browser';
    }

    // Create unique device fingerprint
    $fingerprint = $deviceInfo['os'] . '|' . $deviceInfo['device'] . '|' . $deviceInfo['browser'];

    return $fingerprint;
}

function getDetailedDeviceInfo() {
    $userAgent = $_SERVER['HTTP_USER_AGENT'];
    $deviceInfo = [];

    // Operating System Detection
    if (preg_match('/iPhone OS (\d+[_\d]*)/', $userAgent, $matches)) {
        $deviceInfo['os'] = 'iOS ' . str_replace('_', '.', $matches[1]);
        $deviceInfo['device'] = 'iPhone';
    } elseif (preg_match('/iPad.*OS (\d+[_\d]*)/', $userAgent, $matches)) {
        $deviceInfo['os'] = 'iPadOS ' . str_replace('_', '.', $matches[1]);
        $deviceInfo['device'] = 'iPad';
    } elseif (preg_match('/Android (\d+[\.\d]*)/', $userAgent, $matches)) {
        $deviceInfo['os'] = 'Android ' . $matches[1];
        if (preg_match('/Mobile/', $userAgent)) {
            $deviceInfo['device'] = 'Android Phone';
        } else {
            $deviceInfo['device'] = 'Android Tablet';
        }
    } elseif (preg_match('/Windows NT (\d+\.\d+)/', $userAgent, $matches)) {
        $deviceInfo['os'] = 'Windows ' . $matches[1];
        $deviceInfo['device'] = 'Windows PC';
    } elseif (preg_match('/Mac OS X (\d+[_\d]*)/', $userAgent, $matches)) {
        $deviceInfo['os'] = 'macOS ' . str_replace('_', '.', $matches[1]);
        $deviceInfo['device'] = 'Mac';
    } elseif (preg_match('/Linux/', $userAgent)) {
        $deviceInfo['os'] = 'Linux';
        $deviceInfo['device'] = 'Linux PC';
    } else {
        $deviceInfo['os'] = 'Unknown OS';
        $deviceInfo['device'] = 'Unknown Device';
    }

    // Browser Detection
    if (preg_match('/Chrome\/(\d+[\.\d]*)/', $userAgent, $matches)) {
        $deviceInfo['browser'] = 'Chrome ' . $matches[1];
    } elseif (preg_match('/Firefox\/(\d+[\.\d]*)/', $userAgent, $matches)) {
        $deviceInfo['browser'] = 'Firefox ' . $matches[1];
    } elseif (preg_match('/Safari\/(\d+[\.\d]*)/', $userAgent, $matches) && !preg_match('/Chrome/', $userAgent)) {
        $deviceInfo['browser'] = 'Safari ' . $matches[1];
    } elseif (preg_match('/Edge\/(\d+[\.\d]*)/', $userAgent, $matches)) {
        $deviceInfo['browser'] = 'Edge ' . $matches[1];
    } else {
        $deviceInfo['browser'] = 'Unknown Browser';
    }

    $deviceInfo['user_agent'] = $userAgent;
    $deviceInfo['ip_address'] = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';

    return $deviceInfo;
}

// Generate device token based on user agent and other factors
function generateDeviceToken() {
    $userAgent = $_SERVER['HTTP_USER_AGENT'];
    $ip = $_SERVER['REMOTE_ADDR'];
    $timestamp = time();
    
    return hash('sha256', $userAgent . $ip . $timestamp);
}

// Sanitize input
function sanitizeInput($input) {
    return htmlspecialchars(strip_tags(trim($input)));
}

// Generate registration ID
function generateRegistrationId($staffId) {
    return 'REG_' . $staffId . '_' . date('Ymd_His');
}
?>
