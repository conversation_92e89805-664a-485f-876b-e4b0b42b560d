-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.2
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: Jul 30, 2025 at 09:14 PM (Updated for Clean Rollout)
-- Server version: 8.4.3
-- PHP Version: 8.3.16

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `attendance_db`
--

-- --------------------------------------------------------

--
-- Table structure for table `staff`
--

CREATE TABLE `staff` (
  `id` int NOT NULL,
  `name` varchar(100) NOT NULL,
  `eid_cid_permit` varchar(50) NOT NULL,
  `id_type` enum('CID','Permit','EID') NOT NULL,
  `position_title` varchar(100) DEFAULT NULL,
  `division` varchar(100) DEFAULT NULL,
  `device_token` varchar(255) DEFAULT NULL,
  `device_model` varchar(500) DEFAULT NULL,
  `is_registered` tinyint(1) DEFAULT '0',
  `registered_date` datetime DEFAULT NULL,
  `registration_id` varchar(50) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `staff` (All device fields reset for clean rollout)
--

INSERT INTO `staff` (`id`, `name`, `eid_cid_permit`, `id_type`, `position_title`, `division`, `device_token`, `device_model`, `is_registered`, `registered_date`, `registration_id`) VALUES
(223, 'Aita Singh Tamang', '20200115549', 'EID', 'Meteorology/Hydrology Technician IV', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(224, 'Ajay Pradhan', '9107020', 'EID', 'Meteorology/Hydrology Technician I', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(225, 'Amber Bahadur Rana', '202406927798', 'EID', 'Meteorology/Hydrology Technician III', 'CSD', NULL, NULL, 0, NULL, NULL),
(226, 'Arun Puri', '202501929730', 'EID', 'Meteorology/Hydrology Officer', 'CSD', NULL, NULL, 0, NULL, NULL),
(227, 'Chechey', '200508011', 'EID', 'Admin. Asst. I', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(228, 'Chencho Dema', '202401925685', 'EID', 'Meteorology/Hydrology Officer', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(229, 'Chimi Namgyel', '20170107857', 'EID', 'Sr. Statistical Officer', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(230, 'Choki Wangmo', '202401926421', 'EID', 'ICT Technical Associate II', 'SECT', NULL, NULL, 0, NULL, NULL),
(231, 'Dawa Yangki', '200508221', 'EID', 'Meteorology/Hydrology Technician II', 'MSD', NULL, NULL, 0, NULL, NULL),
(232, 'Dawa Yangchen', '201009126', 'EID', 'Meteorology/Hydrology Technician I', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(233, 'Dechen Wangmo', '20120200082', 'EID', 'Personal Asst. I', 'SECT', NULL, NULL, 0, NULL, NULL),
(234, 'Dungchu Wangdi', '2006044', 'EID', 'Sr. Store Keeper VI', 'SECT', NULL, NULL, 0, NULL, NULL),
(235, 'Harka Bdr. Subba', '200407344', 'EID', 'Driver', 'SECT', NULL, NULL, 0, NULL, NULL),
(236, 'Jamyang Zangpo', '20170107988', 'EID', 'Sr. Meteorology/Hydrology Officer', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(237, 'Jamyang Phuntshok', '200201064', 'EID', 'Principal Meteorology/Hydrology Officer', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(238, 'Jigme Namgyel', '202401925723', 'EID', 'Meteorology/Hydrology Officer', 'MSD', NULL, NULL, 0, NULL, NULL),
(239, 'Jigme Wangdi', '200408043', 'EID', 'Meteorology/Hydrology Technician III', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(240, 'Kajur Tenzin', '200408040', 'EID', 'Meteorology/Hydrology Technician II', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(241, 'Karma', '9909032', 'EID', 'Driver', 'SECT', NULL, NULL, 0, NULL, NULL),
(242, 'Karma Tenzin', '8908009', 'EID', 'Sr. Meteorology/Hydrology Technician III', 'MSD', NULL, NULL, 0, NULL, NULL),
(243, 'Karma Dupchu', '9801010', 'EID', 'Director', 'SECT', NULL, NULL, 0, NULL, NULL),
(244, 'Karma', '9611009', 'EID', 'Specialist II', 'CSD', NULL, NULL, 0, NULL, NULL),
(245, 'Karma Tshering', '200508012', 'EID', 'Meteorology/Hydrology Technician II', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(246, 'Karma Wangchuk', '200508013', 'EID', 'Meteorology/Hydrology Technician II', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(247, 'Karma Wangdi', '200508014', 'EID', 'Meteorology/Hydrology Technician II', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(248, 'Karma Wangmo', '200508015', 'EID', 'Meteorology/Hydrology Technician II', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(249, 'Karma Yangchen', '200508016', 'EID', 'Meteorology/Hydrology Technician II', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(250, 'Karma Yangki', '200508017', 'EID', 'Meteorology/Hydrology Technician II', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(251, 'Karma Yeshi', '200508018', 'EID', 'Meteorology/Hydrology Technician II', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(252, 'Karma Zangmo', '200508019', 'EID', 'Meteorology/Hydrology Technician II', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(253, 'Kuenga Lhamo', '202401925761', 'EID', 'Meteorology/Hydrology Officer', 'CSD', NULL, NULL, 0, NULL, NULL),
(254, 'Kuenga Wangmo', '202401925799', 'EID', 'Meteorology/Hydrology Officer', 'MSD', NULL, NULL, 0, NULL, NULL),
(255, 'Kuenzang Choden', '202401925837', 'EID', 'Meteorology/Hydrology Officer', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(256, 'Kuenzang Lhamo', '202401925875', 'EID', 'Meteorology/Hydrology Officer', 'CSD', NULL, NULL, 0, NULL, NULL),
(257, 'Kuenzang Wangmo', '202401925913', 'EID', 'Meteorology/Hydrology Officer', 'MSD', NULL, NULL, 0, NULL, NULL),
(258, 'Leki Choden', '202401925951', 'EID', 'Meteorology/Hydrology Officer', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(259, 'Lhamo', '200508020', 'EID', 'Meteorology/Hydrology Technician II', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(260, 'Lhamo Dema', '200508021', 'EID', 'Meteorology/Hydrology Technician II', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(261, 'Lhamo Tshering', '200508022', 'EID', 'Meteorology/Hydrology Technician II', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(262, 'Lhamo Wangchuk', '200508023', 'EID', 'Meteorology/Hydrology Technician II', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(263, 'Lhamo Wangdi', '200508024', 'EID', 'Meteorology/Hydrology Technician II', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(264, 'Lhamo Wangmo', '200508025', 'EID', 'Meteorology/Hydrology Technician II', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(265, 'Lhamo Yangchen', '200508026', 'EID', 'Meteorology/Hydrology Technician II', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(266, 'Lhamo Yangki', '200508027', 'EID', 'Meteorology/Hydrology Technician II', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(267, 'Lhamo Yeshi', '200508028', 'EID', 'Meteorology/Hydrology Technician II', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(268, 'Lhamo Zangmo', '200508029', 'EID', 'Meteorology/Hydrology Technician II', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(269, 'Namgay Lhamo', '202401925989', 'EID', 'Meteorology/Hydrology Officer', 'CSD', NULL, NULL, 0, NULL, NULL),
(270, 'Namgay Wangmo', '202401926027', 'EID', 'Meteorology/Hydrology Officer', 'MSD', NULL, NULL, 0, NULL, NULL),
(271, 'Namgyel Wangchuk', '202401926065', 'EID', 'Meteorology/Hydrology Officer', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(272, 'Namgyel Wangdi', '202401926103', 'EID', 'Meteorology/Hydrology Officer', 'CSD', NULL, NULL, 0, NULL, NULL),
(273, 'Namgyel Wangmo', '202401926141', 'EID', 'Meteorology/Hydrology Officer', 'MSD', NULL, NULL, 0, NULL, NULL),
(274, 'Namgyel Yangchen', '202401926179', 'EID', 'Meteorology/Hydrology Officer', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(275, 'Namgyel Yangki', '202401926217', 'EID', 'Meteorology/Hydrology Officer', 'CSD', NULL, NULL, 0, NULL, NULL),
(276, 'Namgyel Yeshi', '202401926255', 'EID', 'Meteorology/Hydrology Officer', 'MSD', NULL, NULL, 0, NULL, NULL),
(277, 'Namgyel Zangmo', '202401926293', 'EID', 'Meteorology/Hydrology Officer', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(278, 'Norbu Wangchuk', '202401926331', 'EID', 'Meteorology/Hydrology Officer', 'CSD', NULL, NULL, 0, NULL, NULL),
(279, 'Norbu Wangdi', '202401926369', 'EID', 'Meteorology/Hydrology Officer', 'MSD', NULL, NULL, 0, NULL, NULL),
(280, 'Norbu Wangmo', '202401926407', 'EID', 'Meteorology/Hydrology Officer', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(281, 'Norbu Yangchen', '202401926445', 'EID', 'Meteorology/Hydrology Officer', 'CSD', NULL, NULL, 0, NULL, NULL),
(282, 'Norbu Yangki', '202401926483', 'EID', 'Meteorology/Hydrology Officer', 'MSD', NULL, NULL, 0, NULL, NULL),
(283, 'Norbu Yeshi', '202401926521', 'EID', 'Meteorology/Hydrology Officer', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(284, 'Norbu Zangmo', '202401926559', 'EID', 'Meteorology/Hydrology Officer', 'CSD', NULL, NULL, 0, NULL, NULL),
(285, 'Pema Choden', '202401926597', 'EID', 'Meteorology/Hydrology Officer', 'MSD', NULL, NULL, 0, NULL, NULL),
(286, 'Pema Lhamo', '202401926635', 'EID', 'Meteorology/Hydrology Officer', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(287, 'Pema Wangmo', '202401926673', 'EID', 'Meteorology/Hydrology Officer', 'CSD', NULL, NULL, 0, NULL, NULL),
(288, 'Sonam Choden', '202401926711', 'EID', 'Meteorology/Hydrology Officer', 'MSD', NULL, NULL, 0, NULL, NULL),
(289, 'Ugyen Tshomo', '20140103335', 'EID', 'Sr. HR Officer', 'SECT', NULL, NULL, 0, NULL, NULL),
(290, 'Wangchuk Dema', '200312012', 'EID', 'Sr. Meteorology/Hydrology Technician III', 'MSD', NULL, NULL, 0, NULL, NULL),
(291, 'Wangdi', '200508228', 'EID', 'Meteorology/Hydrology Technician II', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(292, 'Yangchen', '200805076', 'EID', 'Admin. Asst. I', 'SECT', NULL, NULL, 0, NULL, NULL),
(293, 'Yeshi Choki', '20190112967', 'EID', 'Sr. Meteorology/Hydrology Officer', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(294, 'Yeshi Wangmo', '200508211', 'EID', 'Meteorology/Hydrology Technician II', 'MSD', NULL, NULL, 0, NULL, NULL),
(295, 'Dig Maya Ghalley', '11209000646', 'CID', 'Sweeper', 'SECT', NULL, NULL, 0, NULL, NULL),
(296, 'Thinley Phuntsho', '10603000810', 'CID', 'Project Manager', 'MSD', NULL, NULL, 0, NULL, NULL);

--
-- Indexes for dumped tables
--

--
-- Indexes for table `staff`
--
ALTER TABLE `staff`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `eid_cid_permit` (`eid_cid_permit`),
  ADD UNIQUE KEY `device_token` (`device_token`),
  ADD UNIQUE KEY `device_token_2` (`device_token`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `staff`
--
ALTER TABLE `staff`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=303;
COMMIT;

-- --------------------------------------------------------

--
-- Table structure for table `admins`
--

CREATE TABLE `admins` (
  `id` int NOT NULL,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `admins`
--

INSERT INTO `admins` (`id`, `username`, `password`, `created_at`) VALUES
(1, 'admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '2025-07-08 10:24:52');

-- --------------------------------------------------------

--
-- Table structure for table `user_passwords`
--

CREATE TABLE `user_passwords` (
  `id` int NOT NULL,
  `staff_id` int NOT NULL,
  `password` varchar(255) NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `attendance`
--

CREATE TABLE `attendance` (
  `id` int NOT NULL,
  `staff_id` int NOT NULL,
  `date` date NOT NULL,
  `clock_in` datetime DEFAULT NULL,
  `clock_out` datetime DEFAULT NULL,
  `device_verified` tinyint(1) DEFAULT '1',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `admins`
--
ALTER TABLE `admins`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`);

--
-- Indexes for table `user_passwords`
--
ALTER TABLE `user_passwords`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `staff_id` (`staff_id`),
  ADD KEY `fk_user_passwords_staff` (`staff_id`);

--
-- Indexes for table `attendance`
--
ALTER TABLE `attendance`
  ADD PRIMARY KEY (`id`),
  ADD KEY `staff_id` (`staff_id`),
  ADD KEY `date` (`date`),
  ADD UNIQUE KEY `staff_date` (`staff_id`, `date`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `admins`
--
ALTER TABLE `admins`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `user_passwords`
--
ALTER TABLE `user_passwords`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `attendance`
--
ALTER TABLE `attendance`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `user_passwords`
--
ALTER TABLE `user_passwords`
  ADD CONSTRAINT `fk_user_passwords_staff` FOREIGN KEY (`staff_id`) REFERENCES `staff` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `attendance`
--
ALTER TABLE `attendance`
  ADD CONSTRAINT `fk_attendance_staff` FOREIGN KEY (`staff_id`) REFERENCES `staff` (`id`) ON DELETE CASCADE;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
