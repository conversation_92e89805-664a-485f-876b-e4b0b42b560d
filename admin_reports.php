<?php
require_once 'config/database.php';

requireAdmin();

$pdo = getDBConnection();

// Get date range (default to current month)
$startDate = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-01');
$endDate = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-t');

// Get attendance summary for the date range
$stmt = $pdo->prepare("SELECT s.id, s.name, s.eid_cid_permit, s.position_title, s.division,
                       COUNT(a.id) as days_present,
                       SUM(CASE WHEN a.clock_in IS NOT NULL AND a.clock_out IS NOT NULL THEN 1 ELSE 0 END) as complete_days,
                       SUM(CASE WHEN a.clock_in IS NOT NULL AND a.clock_out IS NULL THEN 1 ELSE 0 END) as incomplete_days
                       FROM staff s
                       LEFT JOIN attendance a ON s.id = a.staff_id AND a.date BETWEEN ? AND ?
                       WHERE s.is_registered = 1
                       GROUP BY s.id, s.name, s.eid_cid_permit, s.position_title, s.division
                       ORDER BY s.name");
$stmt->execute([$startDate, $endDate]);
$attendanceSummary = $stmt->fetchAll();

// Calculate total working days in the range
$start = new DateTime($startDate);
$end = new DateTime($endDate);
$interval = new DateInterval('P1D');
$period = new DatePeriod($start, $interval, $end->modify('+1 day'));

$totalWorkingDays = 0;
foreach ($period as $date) {
    // Exclude weekends (Saturday = 6, Sunday = 0)
    if ($date->format('w') != 0 && $date->format('w') != 6) {
        $totalWorkingDays++;
    }
}

// Get overall statistics
$stmt = $pdo->prepare("SELECT 
                       COUNT(DISTINCT s.id) as total_registered,
                       COUNT(DISTINCT a.staff_id) as staff_with_attendance,
                       COUNT(a.id) as total_attendance_records,
                       SUM(CASE WHEN a.clock_in IS NOT NULL AND a.clock_out IS NOT NULL THEN 1 ELSE 0 END) as complete_records
                       FROM staff s
                       LEFT JOIN attendance a ON s.id = a.staff_id AND a.date BETWEEN ? AND ?
                       WHERE s.is_registered = 1");
$stmt->execute([$startDate, $endDate]);
$overallStats = $stmt->fetch();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Attendance Reports - Admin</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            line-height: 1.6;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            margin: 0;
        }
        
        .nav-links {
            display: flex;
            gap: 1rem;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.2);
            transition: background 0.3s;
        }
        
        .nav-links a:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        
        .controls {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        
        .date-range {
            display: flex;
            align-items: center;
            gap: 1rem;
            flex-wrap: wrap;
        }
        
        .date-range label {
            font-weight: 600;
            color: #333;
        }
        
        .date-range input {
            padding: 0.5rem;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            transform: translateY(-1px);
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .stat-card h3 {
            color: #333;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }
        
        .stat-card .number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }
        
        .summary-table-container {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow-x: auto;
        }
        
        .summary-table {
            width: 100%;
            border-collapse: collapse;
            min-width: 800px;
        }
        
        .summary-table th,
        .summary-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .summary-table th {
            background: #f8f9fa;
            font-weight: 600;
            position: sticky;
            top: 0;
        }
        
        .summary-table tr:hover {
            background: #f8f9fa;
        }
        
        .attendance-rate {
            font-weight: bold;
        }
        
        .rate-excellent {
            color: #28a745;
        }
        
        .rate-good {
            color: #ffc107;
        }
        
        .rate-poor {
            color: #dc3545;
        }
        
        .period-info {
            background: #e9ecef;
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .period-info h4 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        @media (max-width: 768px) {
            .date-range {
                flex-direction: column;
                align-items: stretch;
            }
            
            .date-range > div {
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Attendance Reports</h1>
        <div class="nav-links">
            <a href="admin_dashboard.php">Dashboard</a>
            <a href="admin_attendance.php">Daily Attendance</a>
            <a href="logout.php">Logout</a>
        </div>
    </div>
    
    <div class="container">
        <div class="controls">
            <form method="GET" class="date-range">
                <div>
                    <label for="start_date">From:</label>
                    <input type="date" id="start_date" name="start_date" value="<?php echo $startDate; ?>">
                </div>
                <div>
                    <label for="end_date">To:</label>
                    <input type="date" id="end_date" name="end_date" value="<?php echo $endDate; ?>">
                </div>
                <button type="submit" class="btn">Generate Report</button>
                <button type="button" onclick="printReport()" class="btn">Print Report</button>
            </form>
        </div>
        
        <div class="period-info">
            <h4>Report Period</h4>
            <p><?php echo date('F j, Y', strtotime($startDate)); ?> to <?php echo date('F j, Y', strtotime($endDate)); ?></p>
            <p>Total Working Days: <?php echo $totalWorkingDays; ?></p>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <h3>Total Registered Staff</h3>
                <div class="number"><?php echo $overallStats['total_registered']; ?></div>
            </div>
            <div class="stat-card">
                <h3>Staff with Attendance</h3>
                <div class="number"><?php echo $overallStats['staff_with_attendance']; ?></div>
            </div>
            <div class="stat-card">
                <h3>Total Attendance Records</h3>
                <div class="number"><?php echo $overallStats['total_attendance_records']; ?></div>
            </div>
            <div class="stat-card">
                <h3>Complete Records</h3>
                <div class="number"><?php echo $overallStats['complete_records']; ?></div>
            </div>
        </div>
        
        <div class="summary-table-container">
            <h3 style="margin-bottom: 1rem; color: #333;">Staff Attendance Summary</h3>
            
            <table class="summary-table">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>ID</th>
                        <th>Position</th>
                        <th>Division</th>
                        <th>Days Present</th>
                        <th>Complete Days</th>
                        <th>Incomplete Days</th>
                        <th>Attendance Rate</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($attendanceSummary as $record): ?>
                        <?php 
                        $attendanceRate = $totalWorkingDays > 0 ? ($record['days_present'] / $totalWorkingDays) * 100 : 0;
                        $rateClass = '';
                        if ($attendanceRate >= 90) $rateClass = 'rate-excellent';
                        elseif ($attendanceRate >= 70) $rateClass = 'rate-good';
                        else $rateClass = 'rate-poor';
                        ?>
                        <tr>
                            <td><?php echo htmlspecialchars($record['name']); ?></td>
                            <td><?php echo htmlspecialchars($record['eid_cid_permit']); ?></td>
                            <td><?php echo htmlspecialchars($record['position_title']); ?></td>
                            <td><?php echo htmlspecialchars($record['division']); ?></td>
                            <td><?php echo $record['days_present']; ?></td>
                            <td><?php echo $record['complete_days']; ?></td>
                            <td><?php echo $record['incomplete_days']; ?></td>
                            <td>
                                <span class="attendance-rate <?php echo $rateClass; ?>">
                                    <?php echo number_format($attendanceRate, 1); ?>%
                                </span>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
    
    <script>
        function printReport() {
            window.print();
        }
    </script>
</body>
</html>
