<?php
require_once 'config/database.php';

startSession();

// Redirect if already logged in
if (isUserLoggedIn()) {
    header('Location: attendance.php');
    exit();
}

$pdo = getDBConnection();
$error = '';

// Get all registered staff for the dropdown
$stmt = $pdo->query("SELECT id, name, eid_cid_permit FROM staff WHERE is_registered = 1 ORDER BY name");
$registeredStaff = $stmt->fetchAll();

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $staffId = (int)$_POST['staff_id'];
    $password = $_POST['password'];
    
    if (empty($staffId) || empty($password)) {
        $error = 'Please fill in all fields.';
    } else {
        try {
            // Get staff and password information
            $stmt = $pdo->prepare("SELECT s.*, up.password as user_password 
                                   FROM staff s 
                                   JOIN user_passwords up ON s.id = up.staff_id 
                                   WHERE s.id = ? AND s.is_registered = 1");
            $stmt->execute([$staffId]);
            $user = $stmt->fetch();
            
            if (!$user) {
                $error = 'User not found or not registered.';
            } else {
                // Verify password
                if (!password_verify($password, $user['user_password'])) {
                    $error = 'Invalid password.';
                } else {
                    // Check device verification
                    $currentDevice = getDeviceInfo();
                    $registeredDevice = $user['device_model'];
                    
                    if ($currentDevice !== $registeredDevice) {
                        $error = "Device mismatch! You registered with: {$registeredDevice}, but you're using: {$currentDevice}. Please use the same device you registered with.";
                    } else {
                        // Login successful
                        $_SESSION['user_id'] = $user['id'];
                        $_SESSION['user_name'] = $user['name'];
                        $_SESSION['user_eid'] = $user['eid_cid_permit'];
                        $_SESSION['device_verified'] = true;
                        
                        header('Location: attendance.php');
                        exit();
                    }
                }
            }
        } catch (PDOException $e) {
            $error = 'Database error occurred.';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Login - Attendance System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
        }
        
        .login-container {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 450px;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .login-header h1 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .login-header p {
            color: #666;
        }
        
        .device-info {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1.5rem;
            border-left: 4px solid #667eea;
        }
        
        .device-info h4 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .device-info p {
            color: #666;
            margin: 0;
            font-size: 0.9rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 500;
        }
        
        .form-group select,
        .form-group input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }
        
        .form-group select:focus,
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            width: 100%;
            padding: 0.75rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        
        .error {
            background: #fee;
            color: #c33;
            padding: 0.75rem;
            border-radius: 5px;
            margin-bottom: 1rem;
            border: 1px solid #fcc;
        }
        
        .links {
            text-align: center;
            margin-top: 1rem;
        }
        
        .links a {
            color: #667eea;
            text-decoration: none;
            margin: 0 0.5rem;
            display: inline-block;
            margin-top: 0.5rem;
        }
        
        .links a:hover {
            text-decoration: underline;
        }
        
        .staff-info {
            background: #f8f9fa;
            padding: 0.75rem;
            border-radius: 5px;
            margin-top: 0.5rem;
            display: none;
        }
        
        .staff-info.show {
            display: block;
        }
        
        .warning {
            background: #fff3cd;
            color: #856404;
            padding: 0.75rem;
            border-radius: 5px;
            margin-bottom: 1rem;
            border: 1px solid #ffeaa7;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>User Login</h1>
            <p>Attendance System</p>
        </div>
        
        <div class="device-info">
            <h4>Current Device</h4>
            <?php $deviceDetails = getDetailedDeviceInfo(); ?>
            <p><strong>Device:</strong> <?php echo htmlspecialchars($deviceDetails['device']); ?></p>
            <p><strong>OS:</strong> <?php echo htmlspecialchars($deviceDetails['os']); ?></p>
            <p><strong>Browser:</strong> <?php echo htmlspecialchars($deviceDetails['browser']); ?></p>
            <p><strong>Fingerprint:</strong> <?php echo htmlspecialchars(getDeviceInfo()); ?></p>
        </div>
        
        <div class="warning">
            <strong>Important:</strong> You must use the same device you registered with. Device verification is required for security.
        </div>
        
        <?php if ($error): ?>
            <div class="error"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <?php if (empty($registeredStaff)): ?>
            <div class="error">No registered users found. Please register first.</div>
        <?php else: ?>
            <form method="POST" id="loginForm">
                <div class="form-group">
                    <label for="staff_id">Select Your Name:</label>
                    <select id="staff_id" name="staff_id" required onchange="showStaffInfo()">
                        <option value="">-- Select Your Name --</option>
                        <?php foreach ($registeredStaff as $staff): ?>
                            <option value="<?php echo $staff['id']; ?>" 
                                    data-eid="<?php echo htmlspecialchars($staff['eid_cid_permit']); ?>"
                                    <?php echo (isset($_POST['staff_id']) && $_POST['staff_id'] == $staff['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($staff['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    
                    <div id="staffInfo" class="staff-info">
                        <p><strong>ID:</strong> <span id="staffEid"></span></p>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="password">Password:</label>
                    <input type="password" id="password" name="password" required 
                           placeholder="Enter your password">
                </div>
                
                <button type="submit" class="btn">Login</button>
            </form>
        <?php endif; ?>
        
        <div class="links">
            <a href="user_registration.php">Not registered? Register here</a><br>
            <a href="admin_login.php">Admin Login</a>
        </div>
    </div>
    
    <script>
        function showStaffInfo() {
            const select = document.getElementById('staff_id');
            const staffInfo = document.getElementById('staffInfo');
            const selectedOption = select.options[select.selectedIndex];
            
            if (selectedOption.value) {
                document.getElementById('staffEid').textContent = selectedOption.dataset.eid;
                staffInfo.classList.add('show');
            } else {
                staffInfo.classList.remove('show');
            }
        }
        
        // Show staff info if already selected (after form submission with error)
        window.onload = function() {
            showStaffInfo();
        };
    </script>
</body>
</html>
