<?php
require_once 'config/database.php';

requireAdmin();

$pdo = getDBConnection();

// Get statistics
$stmt = $pdo->query("SELECT COUNT(*) as total_staff FROM staff");
$totalStaff = $stmt->fetch()['total_staff'];

$stmt = $pdo->query("SELECT COUNT(*) as registered_staff FROM staff WHERE is_registered = 1");
$registeredStaff = $stmt->fetch()['registered_staff'];

$stmt = $pdo->query("SELECT COUNT(*) as today_attendance FROM attendance WHERE date = CURDATE()");
$todayAttendance = $stmt->fetch()['today_attendance'];

// Handle user deletion
if (isset($_POST['delete_user'])) {
    $staffId = (int)$_POST['staff_id'];
    try {
        $stmt = $pdo->prepare("UPDATE staff SET is_registered = 0, device_token = NULL, device_model = NULL WHERE id = ?");
        $stmt->execute([$staffId]);
        
        $stmt = $pdo->prepare("DELETE FROM user_passwords WHERE staff_id = ?");
        $stmt->execute([$staffId]);
        
        $success = "User deleted successfully.";
    } catch (PDOException $e) {
        $error = "Error deleting user.";
    }
}

// Get registered users
$stmt = $pdo->query("SELECT s.*, up.created_at as password_created 
                     FROM staff s 
                     LEFT JOIN user_passwords up ON s.id = up.staff_id 
                     WHERE s.is_registered = 1 
                     ORDER BY s.registered_date DESC");
$registeredUsers = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Attendance System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            line-height: 1.6;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            margin: 0;
        }
        
        .logout-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            transition: background 0.3s;
        }
        
        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .stat-card h3 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .stat-card .number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }
        
        .actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .action-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .action-card h3 {
            margin-bottom: 1rem;
            color: #333;
        }
        
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 5px;
            border: none;
            cursor: pointer;
            transition: transform 0.2s;
            margin: 0.25rem;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        }
        
        .qr-container {
            text-align: center;
            margin: 1rem 0;
        }
        
        .users-section {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .users-section h3 {
            margin-bottom: 1rem;
            color: #333;
        }
        
        .user-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        .user-table th,
        .user-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .user-table th {
            background: #f8f9fa;
            font-weight: 600;
        }
        
        .user-table tr:hover {
            background: #f8f9fa;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            padding: 0.75rem;
            border-radius: 5px;
            margin-bottom: 1rem;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 0.75rem;
            border-radius: 5px;
            margin-bottom: 1rem;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Admin Dashboard</h1>
        <a href="logout.php" class="logout-btn">Logout</a>
    </div>
    
    <div class="container">
        <?php if (isset($success)): ?>
            <div class="success"><?php echo $success; ?></div>
        <?php endif; ?>
        
        <?php if (isset($error)): ?>
            <div class="error"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <div class="stats">
            <div class="stat-card">
                <h3>Total Staff</h3>
                <div class="number"><?php echo $totalStaff; ?></div>
            </div>
            <div class="stat-card">
                <h3>Registered Users</h3>
                <div class="number"><?php echo $registeredStaff; ?></div>
            </div>
            <div class="stat-card">
                <h3>Today's Attendance</h3>
                <div class="number"><?php echo $todayAttendance; ?></div>
            </div>
        </div>
        
        <div class="actions">
            <div class="action-card">
                <h3>User Registration</h3>
                <p>Generate QR code for user registration</p>
                <div class="qr-container">
                    <div id="qrcode"></div>
                </div>
                <button onclick="generateQR()" class="btn">Generate QR Code</button>
            </div>
            
            <div class="action-card">
                <h3>Reports & Management</h3>
                <p>View attendance reports and manage users</p>
                <a href="admin_reports.php" class="btn">View Reports</a>
                <a href="admin_attendance.php" class="btn">Daily Attendance</a>
            </div>
        </div>
        
        <div class="users-section">
            <h3>Registered Users (<?php echo count($registeredUsers); ?>)</h3>
            
            <?php if (empty($registeredUsers)): ?>
                <p>No users registered yet.</p>
            <?php else: ?>
                <table class="user-table">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>ID</th>
                            <th>Position</th>
                            <th>Device Model</th>
                            <th>Registered Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($registeredUsers as $user): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($user['name']); ?></td>
                                <td><?php echo htmlspecialchars($user['eid_cid_permit']); ?></td>
                                <td><?php echo htmlspecialchars($user['position_title']); ?></td>
                                <td><?php echo htmlspecialchars($user['device_model']); ?></td>
                                <td><?php echo date('M j, Y g:i A', strtotime($user['registered_date'])); ?></td>
                                <td>
                                    <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this user?')">
                                        <input type="hidden" name="staff_id" value="<?php echo $user['id']; ?>">
                                        <button type="submit" name="delete_user" class="btn btn-danger">Delete</button>
                                    </form>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php endif; ?>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <script>
        function generateQR() {
            const qrContainer = document.getElementById('qrcode');
            qrContainer.innerHTML = '';
            
            const registrationUrl = window.location.origin + window.location.pathname.replace('admin_dashboard.php', 'user_registration.php');
            
            QRCode.toCanvas(qrContainer, registrationUrl, {
                width: 200,
                height: 200,
                colorDark: '#000000',
                colorLight: '#ffffff'
            }, function (error) {
                if (error) console.error(error);
                console.log('QR code generated successfully!');
            });
        }
    </script>
</body>
</html>
