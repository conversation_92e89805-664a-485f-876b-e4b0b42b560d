<?php
require_once 'config/database.php';

startSession();

// Redirect based on existing session
if (isAdminLoggedIn()) {
    header('Location: admin_dashboard.php');
    exit();
} elseif (isUserLoggedIn()) {
    header('Location: attendance.php');
    exit();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Attendance Management System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
        }
        
        .welcome-container {
            background: white;
            padding: 3rem;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 500px;
            width: 100%;
        }
        
        .logo {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 1rem;
        }
        
        .welcome-container h1 {
            color: #333;
            margin-bottom: 1rem;
            font-size: 2rem;
        }
        
        .welcome-container p {
            color: #666;
            margin-bottom: 2rem;
            font-size: 1.1rem;
            line-height: 1.6;
        }
        
        .login-options {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }
        
        .login-btn {
            display: block;
            padding: 1rem 2rem;
            text-decoration: none;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            transition: transform 0.2s, box-shadow 0.2s;
            text-align: center;
        }
        
        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
        
        .admin-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .user-login {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }
        
        .register-link {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: white;
        }
        

        
        @media (max-width: 600px) {
            .welcome-container {
                padding: 2rem;
            }
            
            .welcome-container h1 {
                font-size: 1.5rem;
            }
            
            .logo {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="welcome-container">
        <div class="logo">🕐</div>
        <h1>Attendance Management System</h1>
        <p>Secure, device-verified attendance tracking for your organization</p>
        
        <div class="login-options">
            <a href="admin_login.php" class="login-btn admin-login">
                👨‍💼 Admin Login
            </a>
            <a href="user_login.php" class="login-btn user-login">
                👤 User Login
            </a>
            <a href="user_registration.php" class="login-btn register-link">
                📝 New User Registration
            </a>
            <a href="info.php" class="login-btn" style="background: linear-gradient(135deg, #6c757d 0%, #495057 100%);">
                ℹ️ Access Information & URLs
            </a>
        </div>
        

    </div>
</body>
</html>
