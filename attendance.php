<?php
require_once 'config/database.php';

requireUser();

$pdo = getDBConnection();
$message = '';
$messageType = '';

// Get user information
$stmt = $pdo->prepare("SELECT * FROM staff WHERE id = ?");
$stmt->execute([$_SESSION['user_id']]);
$user = $stmt->fetch();

// Get today's attendance record
$today = date('Y-m-d');
$stmt = $pdo->prepare("SELECT * FROM attendance WHERE staff_id = ? AND date = ?");
$stmt->execute([$_SESSION['user_id'], $today]);
$todayAttendance = $stmt->fetch();

// Handle clock in/out actions
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'];
    
    try {
        if ($action == 'clock_in') {
            if ($todayAttendance && $todayAttendance['clock_in']) {
                $message = 'You have already clocked in today.';
                $messageType = 'error';
            } else {
                if ($todayAttendance) {
                    // Update existing record
                    $stmt = $pdo->prepare("UPDATE attendance SET clock_in = NOW(), device_verified = ? WHERE staff_id = ? AND date = ?");
                    $stmt->execute([$_SESSION['device_verified'] ? 1 : 0, $_SESSION['user_id'], $today]);
                } else {
                    // Create new record
                    $stmt = $pdo->prepare("INSERT INTO attendance (staff_id, date, clock_in, device_verified) VALUES (?, ?, NOW(), ?)");
                    $stmt->execute([$_SESSION['user_id'], $today, $_SESSION['device_verified'] ? 1 : 0]);
                }
                
                $message = 'Successfully clocked in at ' . date('g:i A');
                $messageType = 'success';
                
                // Refresh attendance data
                $stmt = $pdo->prepare("SELECT * FROM attendance WHERE staff_id = ? AND date = ?");
                $stmt->execute([$_SESSION['user_id'], $today]);
                $todayAttendance = $stmt->fetch();
            }
        } elseif ($action == 'clock_out') {
            if (!$todayAttendance || !$todayAttendance['clock_in']) {
                $message = 'You must clock in first before clocking out.';
                $messageType = 'error';
            } elseif ($todayAttendance['clock_out']) {
                $message = 'You have already clocked out today.';
                $messageType = 'error';
            } else {
                $stmt = $pdo->prepare("UPDATE attendance SET clock_out = NOW() WHERE staff_id = ? AND date = ?");
                $stmt->execute([$_SESSION['user_id'], $today]);
                
                $message = 'Successfully clocked out at ' . date('g:i A');
                $messageType = 'success';
                
                // Refresh attendance data
                $stmt = $pdo->prepare("SELECT * FROM attendance WHERE staff_id = ? AND date = ?");
                $stmt->execute([$_SESSION['user_id'], $today]);
                $todayAttendance = $stmt->fetch();
            }
        }
    } catch (PDOException $e) {
        $message = 'Database error occurred.';
        $messageType = 'error';
    }
}

// Get recent attendance history (last 7 days)
$stmt = $pdo->prepare("SELECT * FROM attendance WHERE staff_id = ? ORDER BY date DESC LIMIT 7");
$stmt->execute([$_SESSION['user_id']]);
$recentAttendance = $stmt->fetchAll();

// Calculate working hours for today
$workingHours = '';
if ($todayAttendance && $todayAttendance['clock_in'] && $todayAttendance['clock_out']) {
    $clockIn = new DateTime($todayAttendance['clock_in']);
    $clockOut = new DateTime($todayAttendance['clock_out']);
    $interval = $clockIn->diff($clockOut);
    $workingHours = $interval->format('%h hours %i minutes');
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Attendance - <?php echo htmlspecialchars($user['name']); ?></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 1rem;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .header h1 {
            margin: 0;
        }
        
        .header .user-info {
            text-align: right;
        }
        
        .logout-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            transition: background 0.3s;
            margin-top: 0.5rem;
            display: inline-block;
        }
        
        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .attendance-card {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            margin-bottom: 1rem;
        }
        
        .current-time {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .current-time h2 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .time-display {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 0.5rem;
        }
        
        .date-display {
            color: #666;
            font-size: 1.1rem;
        }
        
        .status-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .status-card {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid #667eea;
        }
        
        .status-card h4 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .status-card .time {
            font-size: 1.2rem;
            font-weight: bold;
            color: #667eea;
        }
        
        .status-card .not-set {
            color: #999;
            font-style: italic;
        }
        
        .action-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 1rem 2rem;
            border: none;
            border-radius: 8px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: transform 0.2s;
            min-width: 150px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn:disabled {
            cursor: not-allowed;
            transform: none;
            opacity: 0.6;
        }
        
        .btn-clock-in {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }
        
        .btn-clock-out {
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
            color: white;
        }
        
        .message {
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .history-section {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }
        
        .history-section h3 {
            color: #333;
            margin-bottom: 1rem;
        }
        
        .history-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .history-table th,
        .history-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .history-table th {
            background: #f8f9fa;
            font-weight: 600;
        }
        
        .history-table tr:hover {
            background: #f8f9fa;
        }
        
        @media (max-width: 600px) {
            .header {
                text-align: center;
            }
            
            .header .user-info {
                text-align: center;
                width: 100%;
                margin-top: 1rem;
            }
            
            .action-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div>
            <h1>Attendance System</h1>
        </div>
        <div class="user-info">
            <div>Welcome, <?php echo htmlspecialchars($user['name']); ?></div>
            <div>ID: <?php echo htmlspecialchars($user['eid_cid_permit']); ?></div>
            <?php $deviceDetails = getDetailedDeviceInfo(); ?>
            <div>Device: <?php echo htmlspecialchars($deviceDetails['device']); ?></div>
            <div>OS: <?php echo htmlspecialchars($deviceDetails['os']); ?></div>
            <a href="user_logout.php" class="logout-btn">Logout</a>
        </div>
    </div>
    
    <div class="container">
        <div class="attendance-card">
            <div class="current-time">
                <h2>Current Time</h2>
                <div class="time-display" id="currentTime"></div>
                <div class="date-display" id="currentDate"></div>
            </div>
            
            <?php if ($message): ?>
                <div class="message <?php echo $messageType; ?>"><?php echo $message; ?></div>
            <?php endif; ?>
            
            <div class="status-section">
                <div class="status-card">
                    <h4>Clock In</h4>
                    <div class="time">
                        <?php if ($todayAttendance && $todayAttendance['clock_in']): ?>
                            <?php echo date('g:i A', strtotime($todayAttendance['clock_in'])); ?>
                        <?php else: ?>
                            <span class="not-set">Not clocked in</span>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="status-card">
                    <h4>Clock Out</h4>
                    <div class="time">
                        <?php if ($todayAttendance && $todayAttendance['clock_out']): ?>
                            <?php echo date('g:i A', strtotime($todayAttendance['clock_out'])); ?>
                        <?php else: ?>
                            <span class="not-set">Not clocked out</span>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="status-card">
                    <h4>Working Hours</h4>
                    <div class="time">
                        <?php if ($workingHours): ?>
                            <?php echo $workingHours; ?>
                        <?php else: ?>
                            <span class="not-set">--</span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <div class="action-buttons">
                <form method="POST" style="display: inline;">
                    <input type="hidden" name="action" value="clock_in">
                    <button type="submit" class="btn btn-clock-in" 
                            <?php echo ($todayAttendance && $todayAttendance['clock_in']) ? 'disabled' : ''; ?>>
                        Clock In
                    </button>
                </form>
                
                <form method="POST" style="display: inline;">
                    <input type="hidden" name="action" value="clock_out">
                    <button type="submit" class="btn btn-clock-out" 
                            <?php echo (!$todayAttendance || !$todayAttendance['clock_in'] || $todayAttendance['clock_out']) ? 'disabled' : ''; ?>>
                        Clock Out
                    </button>
                </form>
            </div>
        </div>
        
        <div class="history-section">
            <h3>Recent Attendance History</h3>
            
            <?php if (empty($recentAttendance)): ?>
                <p>No attendance records found.</p>
            <?php else: ?>
                <table class="history-table">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Clock In</th>
                            <th>Clock Out</th>
                            <th>Working Hours</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($recentAttendance as $record): ?>
                            <tr>
                                <td><?php echo date('M j, Y', strtotime($record['date'])); ?></td>
                                <td>
                                    <?php echo $record['clock_in'] ? date('g:i A', strtotime($record['clock_in'])) : '--'; ?>
                                </td>
                                <td>
                                    <?php echo $record['clock_out'] ? date('g:i A', strtotime($record['clock_out'])) : '--'; ?>
                                </td>
                                <td>
                                    <?php 
                                    if ($record['clock_in'] && $record['clock_out']) {
                                        $clockIn = new DateTime($record['clock_in']);
                                        $clockOut = new DateTime($record['clock_out']);
                                        $interval = $clockIn->diff($clockOut);
                                        echo $interval->format('%h:%I');
                                    } else {
                                        echo '--';
                                    }
                                    ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php endif; ?>
        </div>
    </div>
    
    <script>
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('en-US', {
                hour12: true,
                hour: 'numeric',
                minute: '2-digit',
                second: '2-digit'
            });
            const dateString = now.toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
            
            document.getElementById('currentTime').textContent = timeString;
            document.getElementById('currentDate').textContent = dateString;
        }
        
        // Update time immediately and then every second
        updateTime();
        setInterval(updateTime, 1000);
    </script>
</body>
</html>
