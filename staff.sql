-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.2
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: Jul 10, 2025 at 09:14 PM
-- Server version: 8.4.3
-- PHP Version: 8.3.16

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `attendance_db`
--

-- --------------------------------------------------------

--
-- Table structure for table `staff`
--

CREATE TABLE `staff` (
  `id` int NOT NULL,
  `name` varchar(100) NOT NULL,
  `eid_cid_permit` varchar(50) NOT NULL,
  `id_type` enum('CID','Permit','EID') NOT NULL,
  `position_title` varchar(100) DEFAULT NULL,
  `division` varchar(100) DEFAULT NULL,
  `device_token` varchar(255) DEFAULT NULL,
  `device_model` varchar(500) DEFAULT NULL,
  `is_registered` tinyint(1) DEFAULT '0',
  `registered_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `registration_id` varchar(50) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `staff`
--

INSERT INTO `staff` (`id`, `name`, `eid_cid_permit`, `id_type`, `position_title`, `division`, `device_token`, `device_model`, `is_registered`, `registered_date`, `registration_id`) VALUES
(223, 'Aita Singh Tamang', '20200115549', 'EID', 'Meteorology/Hydrology Technician IV', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(224, 'Ajay Pradhan', '9107020', 'EID', 'Meteorology/Hydrology Technician I', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(225, 'Amber Bahadur Rana', '202406927798', 'EID', 'Meteorology/Hydrology Technician III', 'CSD', NULL, NULL, 0, NULL, NULL),
(226, 'Arun Puri', '202501929730', 'EID', 'Meteorology/Hydrology Officer', 'CSD', NULL, NULL, 0, NULL, NULL),
(227, 'Chechey', '200508011', 'EID', 'Admin. Asst. I', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(228, 'Chencho Dema', '202401925685', 'EID', 'Meteorology/Hydrology Officer', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(229, 'Chimi Namgyel', '20170107857', 'EID', 'Sr. Statistical Officer', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(230, 'Choki Wangmo', '202401926421', 'EID', 'ICT Technical Associate II', 'SECT', NULL, NULL, 0, NULL, NULL),
(231, 'Dawa Yangki', '200508221', 'EID', 'Meteorology/Hydrology Technician II', 'MSD', NULL, NULL, 0, NULL, NULL),
(232, 'Dawa Yangchen', '201009126', 'EID', 'Meteorology/Hydrology Technician I', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(233, 'Dechen Wangmo', '20120200082', 'EID', 'Personal Asst. I', 'SECT', NULL, NULL, 0, NULL, NULL),
(234, 'Dungchu Wangdi', '2006044', 'EID', 'Sr. Store Keeper VI', 'SECT', NULL, NULL, 0, NULL, NULL),
(235, 'Harka Bdr. Subba', '200407344', 'EID', 'Driver', 'SECT', NULL, NULL, 0, NULL, NULL),
(236, 'Jamyang Zangpo', '20170107988', 'EID', 'Sr. Meteorology/Hydrology Officer', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(237, 'Jamyang Phuntshok', '200201064', 'EID', 'Principal Meteorology/Hydrology Officer', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(238, 'Jigme Namgyel', '202401925723', 'EID', 'Meteorology/Hydrology Officer', 'MSD', NULL, NULL, 0, NULL, NULL),
(239, 'Jigme Wangdi', '200408043', 'EID', 'Meteorology/Hydrology Technician III', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(240, 'Kajur Tenzin', '200408040', 'EID', 'Meteorology/Hydrology Technician II', 'HWRSD', NULL, NULL, 0, NULL, NULL),
(241, 'Karma', '9909032', 'EID', 'Driver', 'SECT', NULL, NULL, 0, NULL, NULL),
(242, 'Karma Tenzin', '8908009', 'EID', 'Sr. Meteorology/Hydrology Technician III', 'MSD', NULL, NULL, 0, NULL, NULL),
(243, 'Karma Dupchu', '9801010', 'EID', 'Director', 'SECT', NULL, NULL, 0, NULL, NULL),
(244, 'Karma', '9611009', 'EID', 'Specialist II', 'CSD', NULL, NULL, 0, NULL, NULL),
(245, 'Kezang Jigme', '202403926844', 'EID', 'Asst. Program Officer', 'SECT', '2f7690ac63d54088baa801264f117af0adfba19c7d2fbb54d4391786e423345c', 'Android 11', 1, '2025-07-08 10:24:52', 'REG_245_20250709_211029'),
(246, 'Kinley Namgyel', '201002002', 'EID', 'Meteorology/Hydrology Technician II', 'HWRSD', NULL, NULL, 0, '2025-07-08 10:24:52', NULL),
(247, 'Kinley Tenzin', '202501929451', 'EID', 'Meteorology/Hydrology Officer', 'MSD', 'acf4a49fe2b31b1dfb4d7159919f289419dc221b32a1a377ed8c6b1ae58c52f7', 'iPhone', 1, '2025-07-08 10:24:52', 'REG_247_20250710_162610'),
(248, 'Kinley Tenzin', '202307924193', 'EID', 'Meteorology/Hydrology Technician III', 'MSD', NULL, NULL, 0, '2025-07-08 10:24:52', NULL),
(249, 'Kuenzang', '9907159', 'EID', 'Asst. Engineer', 'HWRSD', '0fc286ffcec674d5b2d28a577350b99b9289a45ea0c0be80678ee0bb4929b8e2', 'Android 10', 1, '2025-07-08 10:24:52', 'REG_249_20250710_204735'),
(250, 'Leki Wangdi', '200204120', 'EID', 'Driver', 'SECT', NULL, NULL, 0, '2025-07-08 10:24:52', NULL),
(251, 'Monju Subba', '20170107981', 'EID', 'Sr. Meteorology/Hydrology Officer', 'MSD', NULL, NULL, 0, '2025-07-08 10:24:52', NULL),
(252, 'Norbu Wangdi', '200204031', 'EID', 'Sr. Meteorology/Hydrology Technician III', 'MSD', '81fe305b1e027f9c518a993c03ea321ad4b74041c193779b37542ce941122850', 'Android 10', 1, '2025-07-08 10:24:52', 'REG_252_20250710_162911'),
(253, 'Pashupati Sharma', '8808020', 'EID', 'Principal Meteorology/Hydrology Officer', 'HWRSD', '6549a6b219e70f6cd7b649fdb1f1dcd4c17be0cad3de160eb7afd0dc2b9def38', 'iPhone', 1, '2025-07-08 10:24:52', 'REG_253_20250710_164301'),
(254, 'Pema Syldon', '20160106525', 'EID', 'Sr. Meteorology/Hydrology Officer', 'MSD', NULL, NULL, 0, '2025-07-08 10:24:52', NULL),
(255, 'Pema Yangden', '9808018', 'EID', 'Sr. Admin. Asst. V', 'SECT', 'c4b75c086d7e6f7f687857ddd75cf012cf95c063b320bb2611c4b88091a765b8', 'iPhone', 1, '2025-07-08 10:24:52', 'REG_255_20250710_160834'),
(256, 'Pema Dorji', '200811013', 'EID', 'Meteorology/Hydrology Technician II', 'MSD', '295199dfd8696eb86c0e2e336a1d8efa1f9cd9f94486453844ba9d9bf0578b15', 'Android 10', 1, '2025-07-08 10:24:52', 'REG_256_20250710_201533'),
(257, 'Pema Dorji', '200811018', 'EID', 'Meteorology/Hydrology Technician II', 'HWRSD', NULL, NULL, 0, '2025-07-08 10:24:52', NULL),
(258, 'Phuntsho Tshering', '200801071', 'EID', 'Principal Meteorology/Hydrology Officer', 'CSD', '4b90993c7f6fffb7d393b585ac0e1ee72d76f623223ad34235057fadbebe2e20', 'Android 13', 1, '2025-07-08 10:24:52', 'REG_258_20250709_222050'),
(259, 'Phurpa Wangdi', '8704026', 'EID', 'Sr. Meteorology/Hydrology Technician III', 'HWRSD', NULL, NULL, 0, '2025-07-08 10:24:52', NULL),
(260, 'Ranjit Tamang', '8908010', 'EID', 'Sr. Meteorology/Hydrology Technician III', 'MSD', NULL, NULL, 0, '2025-07-08 10:24:52', NULL),
(261, 'Renu Kumari Thapa', '9505029', 'EID', 'Sr. Laboratory Asst. IV', 'HWRSD', 'e03753aa7cc1a05c420c06c7c91f6fa81f2a1714ea863dccdcfd2d3b39cf08d0', 'iPhone', 1, '2025-07-08 10:24:52', 'REG_261_20250710_202729'),
(262, 'Rinchen Namgay', '9305034', 'EID', 'Driver', 'SECT', NULL, NULL, 0, '2025-07-08 10:24:52', NULL),
(263, 'Sangay Wangmo', '20200115546', 'EID', 'Meteorology/Hydrology Technician IV', 'HWRSD', NULL, NULL, 0, '2025-07-08 10:24:52', NULL),
(264, 'Sangay Tenzin', '9901165', 'EID', 'Asst. Engineer', 'HWRSD', NULL, NULL, 0, '2025-07-08 10:24:52', NULL),
(265, 'Sangay Tshering', '202301923016', 'EID', 'Meteorology/Hydrology Officer', 'MSD', NULL, NULL, 0, '2025-07-08 10:24:52', NULL),
(266, 'Saroj Acharya', '20200116264', 'EID', 'Sr. Meteorology/Hydrology Officer', 'TSRD', '00352443690a0244848fd6bec0398ceb0d75a4f082cba3e24dafbf4f3d9b07bd', 'Android 10', 1, '2025-07-08 10:24:52', 'REG_266_20250709_220215'),
(267, 'Sherub Phuntsho', '200701045', 'EID', 'Principal Meteorology/Hydrology Officer', 'TSRD', NULL, NULL, 0, '2025-07-08 10:24:52', NULL),
(268, 'Singay Dorji', '2101065', 'EID', 'Specialist III', 'MSD', 'f481c4a90340d12e4468b167201097189b84b0b6334cf782dc964c70c1dbd48c', 'iPhone', 1, '2025-07-08 10:24:52', 'REG_268_20250710_163722'),
(269, 'Sonam Zangmo', '9505027', 'EID', 'Sr. Laboratory Asst. V', 'HWRSD', '147c43eedf1ae8a0a9c36275ae40c7a7a6a49fa9ffe832bf9edf13ddab8fa8b8', 'Android 10', 1, '2025-07-08 10:24:52', 'REG_269_20250710_202629'),
(270, 'Sonam Tshewang', '9704025', 'EID', 'Driver', 'SECT', NULL, NULL, 0, '2025-07-08 10:24:52', NULL),
(271, 'Sonam Lhamo', '201001106', 'EID', 'Principal Meteorology/Hydrology Officer', 'CSD', NULL, NULL, 0, '2025-07-08 10:24:52', NULL),
(272, 'Sonam Dorji', '20170108483', 'EID', 'Meteorology/Hydrology Technician IV', 'HWRSD', 'b846269c524e3d634b467228f706a2572e2a5f63db96fce1727850a934a753e3', 'iPhone', 1, '2025-07-08 10:24:52', 'REG_272_20250710_204623'),
(273, 'Sonam Tashi', '20130702235', 'EID', 'Meteorology/Hydrology Technician III', 'TSRD', NULL, NULL, 0, '2025-07-08 10:24:52', NULL),
(274, 'Sonam Tshering', '20130902756', 'EID', 'Driver', 'SECT', NULL, NULL, 0, '2025-07-08 10:24:52', NULL),
(275, 'Sonam Wangmo', '20121200995', 'EID', 'Meteorology/Hydrology Technician III', 'MSD', '439667666b5f830bd405a2d62a61c8bce6bb201718229ee464c2eba2b729c8e5', 'Android 10', 1, '2025-07-08 10:24:52', 'REG_275_20250710_162321'),
(276, 'Sonam Tenzin Yuedsel', '202301923169', 'EID', 'ICT Technical Associate II', 'SECT', '648c85cbd954153403e5a998e5b0e0fde02a32616b77945a1f2847dbf8ade839', 'Android 10', 1, '2025-07-08 10:24:52', 'REG_276_20250710_151916'),
(277, 'Sonam Lhamo', '20200115547', 'EID', 'Meteorology/Hydrology Technician IV', 'MSD', NULL, NULL, 0, '2025-07-08 10:24:52', NULL),
(278, 'Tandin Wangchuk', '20150105022', 'EID', 'Dy. Chief Meteorology/Hydrology Officer', 'HWRSD', NULL, NULL, 0, '2025-07-08 10:24:52', NULL),
(279, 'Tashi Jamtsho', '20120200081', 'EID', 'Driver', 'SECT', 'e621ae5b3a6a9b089170c205975183cc1f3bd2b9187afbd45921b5924c17fc1b', 'Android 10', 1, '2025-07-08 10:24:52', 'REG_279_20250710_161414'),
(280, 'Thinley Gyelmo', '9901225', 'EID', 'Sr. Admin. Asst. V', 'SECT', '35d72e822c24582cad1d1d768430c9e0fdb664b61c89af926b586f171f39acff', 'iPhone', 1, '2025-07-08 10:24:52', 'REG_280_20250710_160742'),
(281, 'Trashi Namgyal', '201201111', 'EID', 'Dy. Chief Meteorology/Hydrology Officer', 'TSRD', '0f0ba819a5bdfce6ea72f8f9d156dcb7d562a4e49e8b4a25e7ce48857fe71fa4', 'iPhone', 1, '2025-07-08 10:24:52', 'REG_281_20250709_221718'),
(282, 'Tshering Choden', '20120100419', 'EID', 'Meteorology/Hydrology Technician II', 'MSD', NULL, NULL, 0, '2025-07-08 10:24:52', NULL),
(283, 'Tshewang Jamtsho', '202401925672', 'EID', 'Meteorology/Hydrology Officer', 'CSD', 'a4b3a5702526705cb70301306456f9712e2a3c3bd0a4e7577408f32110b178da', 'Android 10', 1, '2025-07-08 10:24:52', 'REG_283_20250710_151719'),
(284, 'Tshewang Gyeltshen', '202501929469', 'EID', 'Meteorology/Hydrology Officer', 'MSD', NULL, NULL, 0, '2025-07-08 10:24:52', NULL),
(285, 'Sudhi Janardhanan Radhamany', '1414103831514945', 'Permit', 'Technical Maintenance Officer', 'FWS', NULL, NULL, 0, '2025-07-08 10:24:52', NULL),
(286, 'Ugyen Tshering', '202307924314', 'EID', 'Meteorology/Hydrology Technician III', 'MSD', NULL, NULL, 0, '2025-07-08 10:24:52', NULL),
(287, 'Ugyen Dema', '202406927976', 'EID', 'Asst. Procurement Officer', 'SECT', '0789f916fdf947b70ad738d7478bf027279a73cc725843bf927aa43e9751ea87', 'iPhone', 1, '2025-07-08 10:24:52', 'REG_287_20250710_161020'),
(288, 'Ugyen Chophel', '201201032', 'EID', 'Dy. Chief Statistical Officer', 'MSD', NULL, NULL, 0, '2025-07-08 10:24:52', NULL),
(289, 'Ugyen Tshomo', '20140103335', 'EID', 'Sr. HR Officer', 'SECT', NULL, NULL, 0, '2025-07-08 10:24:52', NULL),
(290, 'Wangchuk Dema', '200312012', 'EID', 'Sr. Meteorology/Hydrology Technician III', 'MSD', 'f12bd89770dd7d123d5855eae462955f5209a736a905319b91917d86c53aa5fe', 'iPhone', 1, '2025-07-08 10:24:52', 'REG_290_20250710_210821'),
(291, 'Wangdi', '200508228', 'EID', 'Meteorology/Hydrology Technician II', 'HWRSD', NULL, NULL, 0, '2025-07-08 10:24:52', NULL),
(292, 'Yangchen', '200805076', 'EID', 'Admin. Asst. I', 'SECT', NULL, NULL, 0, '2025-07-08 10:24:52', NULL),
(293, 'Yeshi Choki', '20190112967', 'EID', 'Sr. Meteorology/Hydrology Officer', 'HWRSD', '81d61ae52fabe333aeccad017d14c6cbd5f4d46636174e676c21ff02e2b5f354', 'iPhone', 1, '2025-07-08 10:24:52', 'REG_293_20250710_202516'),
(294, 'Yeshi Wangmo', '200508211', 'EID', 'Meteorology/Hydrology Technician II', 'MSD', NULL, NULL, 0, '2025-07-08 10:24:52', NULL),
(295, 'Dig Maya Ghalley', '11209000646', 'CID', 'Sweeper', 'SECT', '9d274489a7f5ff12b5232f737bffd03beea7113b7ec730b7859ee398e8db7f34', 'Android 10', 1, '2025-07-08 10:24:52', 'REG_295_20250710_202820'),
(296, 'Thinley Phuntsho', '10603000810', 'CID', 'Project Manager', 'MSD', '732569a9ad1971eaf2830620277e89a55d9767539f9428ea7cd3da1ab1fe6d3e', 'Android 10', 1, '2025-07-08 10:24:52', 'REG_296_20250710_163942');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `staff`
--
ALTER TABLE `staff`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `eid_cid_permit` (`eid_cid_permit`),
  ADD UNIQUE KEY `device_token` (`device_token`),
  ADD UNIQUE KEY `device_token_2` (`device_token`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `staff`
--
ALTER TABLE `staff`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=303;
COMMIT;

-- --------------------------------------------------------

--
-- Table structure for table `admins`
--

CREATE TABLE `admins` (
  `id` int NOT NULL,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `admins`
--

INSERT INTO `admins` (`id`, `username`, `password`) VALUES
(1, 'admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'); -- password: password

-- --------------------------------------------------------

--
-- Table structure for table `user_passwords`
--

CREATE TABLE `user_passwords` (
  `id` int NOT NULL,
  `staff_id` int NOT NULL,
  `password` varchar(255) NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `attendance`
--

CREATE TABLE `attendance` (
  `id` int NOT NULL,
  `staff_id` int NOT NULL,
  `clock_in` datetime DEFAULT NULL,
  `clock_out` datetime DEFAULT NULL,
  `date` date NOT NULL,
  `device_verified` tinyint(1) DEFAULT '1',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Indexes for table `admins`
--
ALTER TABLE `admins`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`);

--
-- Indexes for table `user_passwords`
--
ALTER TABLE `user_passwords`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `staff_id` (`staff_id`),
  ADD KEY `fk_user_passwords_staff` (`staff_id`);

--
-- Indexes for table `attendance`
--
ALTER TABLE `attendance`
  ADD PRIMARY KEY (`id`),
  ADD KEY `staff_id` (`staff_id`),
  ADD KEY `date` (`date`),
  ADD UNIQUE KEY `staff_date` (`staff_id`, `date`);

--
-- AUTO_INCREMENT for table `admins`
--
ALTER TABLE `admins`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `user_passwords`
--
ALTER TABLE `user_passwords`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `attendance`
--
ALTER TABLE `attendance`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- Constraints for table `user_passwords`
--
ALTER TABLE `user_passwords`
  ADD CONSTRAINT `fk_user_passwords_staff` FOREIGN KEY (`staff_id`) REFERENCES `staff` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `attendance`
--
ALTER TABLE `attendance`
  ADD CONSTRAINT `fk_attendance_staff` FOREIGN KEY (`staff_id`) REFERENCES `staff` (`id`) ON DELETE CASCADE;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
