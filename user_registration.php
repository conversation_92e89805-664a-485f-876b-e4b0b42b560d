<?php
require_once 'config/database.php';

$pdo = getDBConnection();
$error = '';
$success = '';

// Get all unregistered staff
$stmt = $pdo->query("SELECT id, name, eid_cid_permit, position_title, division FROM staff WHERE is_registered = 0 ORDER BY name");
$availableStaff = $stmt->fetchAll();

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $staffId = (int)$_POST['staff_id'];
    $password = $_POST['password'];
    $confirmPassword = $_POST['confirm_password'];
    
    if (empty($staffId) || empty($password) || empty($confirmPassword)) {
        $error = 'Please fill in all fields.';
    } elseif ($password !== $confirmPassword) {
        $error = 'Passwords do not match.';
    } elseif (strlen($password) < 6) {
        $error = 'Password must be at least 6 characters long.';
    } else {
        try {
            // Get device information
            $deviceModel = getDeviceInfo();
            $deviceToken = generateDeviceToken();
            $registrationId = generateRegistrationId($staffId);
            
            // Start transaction
            $pdo->beginTransaction();
            
            // Update staff record
            $stmt = $pdo->prepare("UPDATE staff SET 
                                   device_token = ?, 
                                   device_model = ?, 
                                   is_registered = 1, 
                                   registered_date = NOW(), 
                                   registration_id = ? 
                                   WHERE id = ? AND is_registered = 0");
            $result = $stmt->execute([$deviceToken, $deviceModel, $registrationId, $staffId]);
            
            if ($stmt->rowCount() == 0) {
                throw new Exception('Staff member not found or already registered.');
            }
            
            // Insert password
            $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("INSERT INTO user_passwords (staff_id, password) VALUES (?, ?)");
            $stmt->execute([$staffId, $hashedPassword]);
            
            // Commit transaction
            $pdo->commit();
            
            $success = 'Registration successful! You can now login with your credentials.';
            
            // Refresh available staff list
            $stmt = $pdo->query("SELECT id, name, eid_cid_permit, position_title, division FROM staff WHERE is_registered = 0 ORDER BY name");
            $availableStaff = $stmt->fetchAll();
            
        } catch (Exception $e) {
            $pdo->rollBack();
            $error = 'Registration failed: ' . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Registration - Attendance System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 1rem;
        }
        
        .registration-container {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 500px;
            margin: 0 auto;
        }
        
        .registration-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .registration-header h1 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .registration-header p {
            color: #666;
        }
        
        .device-info {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1.5rem;
            border-left: 4px solid #667eea;
        }
        
        .device-info h4 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .device-info p {
            color: #666;
            margin: 0.25rem 0;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 500;
        }
        
        .form-group select,
        .form-group input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }
        
        .form-group select:focus,
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            width: 100%;
            padding: 0.75rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        
        .error {
            background: #fee;
            color: #c33;
            padding: 0.75rem;
            border-radius: 5px;
            margin-bottom: 1rem;
            border: 1px solid #fcc;
        }
        
        .success {
            background: #efe;
            color: #3c3;
            padding: 0.75rem;
            border-radius: 5px;
            margin-bottom: 1rem;
            border: 1px solid #cfc;
        }
        
        .links {
            text-align: center;
            margin-top: 1rem;
        }
        
        .links a {
            color: #667eea;
            text-decoration: none;
            margin: 0 1rem;
        }
        
        .links a:hover {
            text-decoration: underline;
        }
        
        .staff-info {
            background: #f8f9fa;
            padding: 0.75rem;
            border-radius: 5px;
            margin-top: 0.5rem;
            display: none;
        }
        
        .staff-info.show {
            display: block;
        }
    </style>
</head>
<body>
    <div class="registration-container">
        <div class="registration-header">
            <h1>User Registration</h1>
            <p>Register for the Attendance System</p>
        </div>
        
        <div class="device-info">
            <h4>Device Information</h4>
            <?php $deviceDetails = getDetailedDeviceInfo(); ?>
            <p><strong>Device:</strong> <?php echo htmlspecialchars($deviceDetails['device']); ?></p>
            <p><strong>Operating System:</strong> <?php echo htmlspecialchars($deviceDetails['os']); ?></p>
            <p><strong>Browser:</strong> <?php echo htmlspecialchars($deviceDetails['browser']); ?></p>
            <p><strong>IP Address:</strong> <?php echo htmlspecialchars($deviceDetails['ip_address']); ?></p>
            <p><strong>Device Fingerprint:</strong> <?php echo htmlspecialchars(getDeviceInfo()); ?></p>
            <p><em>This device information will be linked to your account for security purposes.</em></p>
        </div>
        
        <?php if ($error): ?>
            <div class="error"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <?php if ($success): ?>
            <div class="success"><?php echo $success; ?></div>
        <?php endif; ?>
        
        <?php if (empty($availableStaff)): ?>
            <div class="error">No staff members available for registration. All staff may already be registered.</div>
        <?php else: ?>
            <form method="POST" id="registrationForm">
                <div class="form-group">
                    <label for="staff_id">Select Your Name:</label>
                    <select id="staff_id" name="staff_id" required onchange="showStaffInfo()">
                        <option value="">-- Select Your Name --</option>
                        <?php foreach ($availableStaff as $staff): ?>
                            <option value="<?php echo $staff['id']; ?>" 
                                    data-eid="<?php echo htmlspecialchars($staff['eid_cid_permit']); ?>"
                                    data-position="<?php echo htmlspecialchars($staff['position_title']); ?>"
                                    data-division="<?php echo htmlspecialchars($staff['division']); ?>">
                                <?php echo htmlspecialchars($staff['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    
                    <div id="staffInfo" class="staff-info">
                        <p><strong>ID:</strong> <span id="staffEid"></span></p>
                        <p><strong>Position:</strong> <span id="staffPosition"></span></p>
                        <p><strong>Division:</strong> <span id="staffDivision"></span></p>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="password">Create Password:</label>
                    <input type="password" id="password" name="password" required minlength="6" 
                           placeholder="Minimum 6 characters">
                </div>
                
                <div class="form-group">
                    <label for="confirm_password">Confirm Password:</label>
                    <input type="password" id="confirm_password" name="confirm_password" required 
                           placeholder="Re-enter your password">
                </div>
                
                <button type="submit" class="btn">Register</button>
            </form>
        <?php endif; ?>
        
        <div class="links">
            <a href="user_login.php">Already registered? Login here</a>
            <a href="admin_login.php">Admin Login</a>
        </div>
    </div>
    
    <script>
        function showStaffInfo() {
            const select = document.getElementById('staff_id');
            const staffInfo = document.getElementById('staffInfo');
            const selectedOption = select.options[select.selectedIndex];
            
            if (selectedOption.value) {
                document.getElementById('staffEid').textContent = selectedOption.dataset.eid;
                document.getElementById('staffPosition').textContent = selectedOption.dataset.position;
                document.getElementById('staffDivision').textContent = selectedOption.dataset.division;
                staffInfo.classList.add('show');
            } else {
                staffInfo.classList.remove('show');
            }
        }
        
        // Password confirmation validation
        document.getElementById('confirm_password').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;
            
            if (password !== confirmPassword) {
                this.setCustomValidity('Passwords do not match');
            } else {
                this.setCustomValidity('');
            }
        });
    </script>
</body>
</html>
