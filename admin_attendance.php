<?php
require_once 'config/database.php';

requireAdmin();

$pdo = getDBConnection();

// Get selected date (default to today)
$selectedDate = isset($_GET['date']) ? $_GET['date'] : date('Y-m-d');

// Get attendance records for the selected date
$stmt = $pdo->prepare("SELECT s.name, s.eid_cid_permit, s.position_title, s.division, s.device_model,
                       a.clock_in, a.clock_out, a.device_verified
                       FROM staff s
                       LEFT JOIN attendance a ON s.id = a.staff_id AND a.date = ?
                       WHERE s.is_registered = 1
                       ORDER BY s.name");
$stmt->execute([$selectedDate]);
$attendanceRecords = $stmt->fetchAll();

// Calculate statistics
$totalRegistered = count($attendanceRecords);
$presentCount = 0;
$clockedInCount = 0;
$clockedOutCount = 0;

foreach ($attendanceRecords as $record) {
    if ($record['clock_in']) {
        $presentCount++;
        $clockedInCount++;
    }
    if ($record['clock_out']) {
        $clockedOutCount++;
    }
}

$absentCount = $totalRegistered - $presentCount;
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daily Attendance - Admin</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            line-height: 1.6;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            margin: 0;
        }
        
        .nav-links {
            display: flex;
            gap: 1rem;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.2);
            transition: background 0.3s;
        }
        
        .nav-links a:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        
        .controls {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }
        
        .date-selector {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .date-selector label {
            font-weight: 600;
            color: #333;
        }
        
        .date-selector input {
            padding: 0.5rem;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            transform: translateY(-1px);
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .stat-card h3 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .stat-card .number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }
        
        .attendance-table-container {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow-x: auto;
        }
        
        .attendance-table {
            width: 100%;
            border-collapse: collapse;
            min-width: 800px;
        }
        
        .attendance-table th,
        .attendance-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .attendance-table th {
            background: #f8f9fa;
            font-weight: 600;
            position: sticky;
            top: 0;
        }
        
        .attendance-table tr:hover {
            background: #f8f9fa;
        }
        
        .status-present {
            color: #28a745;
            font-weight: bold;
        }
        
        .status-absent {
            color: #dc3545;
            font-weight: bold;
        }
        
        .status-partial {
            color: #ffc107;
            font-weight: bold;
        }
        
        .device-verified {
            color: #28a745;
            font-size: 0.8rem;
        }
        
        .device-not-verified {
            color: #dc3545;
            font-size: 0.8rem;
        }
        
        .export-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        
        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
                align-items: stretch;
            }
            
            .date-selector {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Daily Attendance Report</h1>
        <div class="nav-links">
            <a href="admin_dashboard.php">Dashboard</a>
            <a href="admin_reports.php">Reports</a>
            <a href="logout.php">Logout</a>
        </div>
    </div>
    
    <div class="container">
        <div class="controls">
            <div class="date-selector">
                <label for="date">Select Date:</label>
                <input type="date" id="date" value="<?php echo $selectedDate; ?>" onchange="changeDate()">
            </div>
            <div>
                <button onclick="printReport()" class="btn">Print Report</button>
            </div>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <h3>Total Registered</h3>
                <div class="number"><?php echo $totalRegistered; ?></div>
            </div>
            <div class="stat-card">
                <h3>Present</h3>
                <div class="number"><?php echo $presentCount; ?></div>
            </div>
            <div class="stat-card">
                <h3>Absent</h3>
                <div class="number"><?php echo $absentCount; ?></div>
            </div>
            <div class="stat-card">
                <h3>Clocked Out</h3>
                <div class="number"><?php echo $clockedOutCount; ?></div>
            </div>
        </div>
        
        <div class="attendance-table-container">
            <h3 style="margin-bottom: 1rem; color: #333;">
                Attendance for <?php echo date('F j, Y', strtotime($selectedDate)); ?>
            </h3>
            
            <table class="attendance-table">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>ID</th>
                        <th>Position</th>
                        <th>Division</th>
                        <th>Device Model</th>
                        <th>Clock In</th>
                        <th>Clock Out</th>
                        <th>Working Hours</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($attendanceRecords as $record): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($record['name']); ?></td>
                            <td><?php echo htmlspecialchars($record['eid_cid_permit']); ?></td>
                            <td><?php echo htmlspecialchars($record['position_title']); ?></td>
                            <td><?php echo htmlspecialchars($record['division']); ?></td>
                            <td>
                                <?php echo htmlspecialchars($record['device_model']); ?>
                                <?php if ($record['clock_in']): ?>
                                    <br>
                                    <span class="<?php echo $record['device_verified'] ? 'device-verified' : 'device-not-verified'; ?>">
                                        <?php echo $record['device_verified'] ? '✓ Verified' : '⚠ Not Verified'; ?>
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php echo $record['clock_in'] ? date('g:i A', strtotime($record['clock_in'])) : '--'; ?>
                            </td>
                            <td>
                                <?php echo $record['clock_out'] ? date('g:i A', strtotime($record['clock_out'])) : '--'; ?>
                            </td>
                            <td>
                                <?php 
                                if ($record['clock_in'] && $record['clock_out']) {
                                    $clockIn = new DateTime($record['clock_in']);
                                    $clockOut = new DateTime($record['clock_out']);
                                    $interval = $clockIn->diff($clockOut);
                                    echo $interval->format('%h:%I');
                                } else {
                                    echo '--';
                                }
                                ?>
                            </td>
                            <td>
                                <?php 
                                if ($record['clock_in'] && $record['clock_out']) {
                                    echo '<span class="status-present">Present</span>';
                                } elseif ($record['clock_in']) {
                                    echo '<span class="status-partial">Clocked In</span>';
                                } else {
                                    echo '<span class="status-absent">Absent</span>';
                                }
                                ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
    
    <script>
        function changeDate() {
            const date = document.getElementById('date').value;
            window.location.href = '?date=' + date;
        }

        function printReport() {
            window.print();
        }
    </script>
</body>
</html>
